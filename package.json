{"name": "cleanify-auto-upload", "version": "1.0.0", "description": "Auto upload image to Cleanify using IPFS and daily action APIs", "main": "upload.js", "type": "module", "scripts": {"start": "node upload.js", "upload": "node upload.js"}, "dependencies": {"@vechain/sdk-core": "^2.0.0", "@vechain/sdk-network": "^2.0.0", "ethers": "^6.8.1", "form-data": "^4.0.0", "keccak": "^3.0.4", "node-fetch": "^3.3.2"}, "keywords": ["cleanify", "ipfs", "upload", "automation"], "author": "", "license": "MIT"}